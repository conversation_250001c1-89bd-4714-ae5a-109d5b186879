<script setup>
import { isEmpty } from 'lodash-es';
import { onMounted } from 'vue';
import HawkEditableName from '~/common/components/molecules/hawk-editable-name.vue';

const props = defineProps({
  // NOTE: For the future, to edit existing widgets
  widgetDetails: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['close']);

const state = reactive({
  is_renaming: false,
  widget_details: {},
});

function handleNameChange(name) {
  state.widget_details.name = name;
  state.is_renaming = false;
}

onMounted(() => {
  state.widget_details = props.widgetDetails;
  if (isEmpty(state.widget_details))
    state.is_renaming = true;
});
</script>

<template>
  <HawkModalContainer content_class="h-full w-full rounded-none">
    <div class="col-span-12">
      <HawkModalHeader class="h-[92px]" @close="emit('close')">
        <template #title>
          <div class="font-semibold text-lg">
            <HawkEditableName
              v-if="state.is_renaming"
              class="max-w-[50vw]"
              :name="state.widget_details.name"
              placeholder="Enter name of the widget"
              :input_classes="{
                TextElement: {
                  input: 'font-semibold !text-xl !p-0',
                  inputContainer: 'border-0 border-b',
                },
                ElementLayout: {
                  innerWrapper: 'border-b',
                },
              }"
              @close="state.is_renaming = false"
              @update="handleNameChange($event)"
            />
            <div v-else class="group flex items-center gap-3 text-xl text-gray-900 font-semibold max-w-[65vw] mt-[3px]" @click="state.is_renaming = true">
              <span>
                {{ state.widget_details.name }}
              </span>
              <IconHawkEditOne class="w-4 h-4 group-hover:visible invisible cursor-pointer" @click="state.is_renaming = true" />
            </div>
          </div>
        </template>
      </HawkModalHeader>
      <HawkModalContent class="">
        CONTENT
      </HawkModalContent>
    </div>
  </HawkModalContainer>
</template>
